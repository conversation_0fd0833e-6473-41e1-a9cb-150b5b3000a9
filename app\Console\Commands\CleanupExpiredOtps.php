<?php

namespace App\Console\Commands;

use App\Models\EmailVerification;
use Illuminate\Console\Command;

class CleanupExpiredOtps extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otp:cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired OTP tokens from the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $deletedCount = EmailVerification::cleanupExpired();
        
        $this->info("Cleaned up {$deletedCount} expired OTP tokens.");
        
        return Command::SUCCESS;
    }
}
