<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class EmailVerificationMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The verification token.
     *
     * @var string
     */
    public $token;

    /**
     * The verification type.
     *
     * @var string
     */
    public $type;

    /**
     * Create a new message instance.
     *
     * @param string $token
     * @param string $type
     */
    public function __construct($token, $type = 'registration')
    {
        $this->token = $token;
        $this->type = $type;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = match($this->type) {
            'registration' => 'Verify Your Email Address',
            'login' => 'Your Login OTP Code',
            'password_reset' => 'Reset Your Password',
            default => 'Your Verification Code',
        };

        return new Envelope(
            subject: $subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $view = match($this->type) {
            'registration' => 'emails.verification',
            'login' => 'emails.login-otp',
            'password_reset' => 'emails.password-reset',
            default => 'emails.verification',
        };

        return new Content(
            view: $view,
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
