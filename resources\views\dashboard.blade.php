<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - {{ config('app.name') }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }

        .navbar {
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }

        .nav-links {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .nav-links a:hover {
            background: #f8f9fa;
        }

        .logout-btn {
            background: #dc3545;
            color: white !important;
            border: none;
            cursor: pointer;
        }

        .logout-btn:hover {
            background: #c82333 !important;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .welcome-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 40px;
        }

        .welcome-section h1 {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .welcome-section p {
            font-size: 20px;
            opacity: 0.9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 16px;
        }

        .features-section {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .features-section h2 {
            text-align: center;
            margin-bottom: 40px;
            color: #333;
            font-size: 32px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .feature-card {
            padding: 30px;
            border: 2px solid #f8f9fa;
            border-radius: 10px;
            transition: border-color 0.3s ease;
        }

        .feature-card:hover {
            border-color: #007bff;
        }

        .feature-card h3 {
            color: #007bff;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .user-info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .user-info h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .user-detail {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .user-detail:last-child {
            border-bottom: none;
        }

        .user-detail strong {
            color: #333;
        }

        .user-detail span {
            color: #666;
        }

        .two-factor-toggle {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .toggle-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-btn:hover {
            background: #218838;
        }

        .toggle-btn.disabled {
            background: #dc3545;
        }

        .toggle-btn.disabled:hover {
            background: #c82333;
        }
    </style>
</head>
<body>

<nav class="navbar">
    <div class="nav-container">
        <div class="logo">{{ config('app.name') }}</div>
        <div class="nav-links">
            <a href="{{ route('dashboard') }}">Dashboard</a>
            <a href="{{ route('posts.index') }}">Posts</a>
            <span>Welcome, {{ Auth::user()->name }}!</span>
            <form action="{{ route('logout') }}" method="POST" style="display: inline;">
                @csrf
                <button type="submit" class="nav-links logout-btn">Logout</button>
            </form>
        </div>
    </div>
</nav>

<div class="container">
    @if(session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @endif

    <div class="welcome-section">
        <h1>Welcome Back!</h1>
        <p>You're successfully logged in to your secure dashboard</p>
    </div>

    <div class="user-info">
        <h3>Account Information</h3>
        <div class="user-detail">
            <strong>Name:</strong>
            <span>{{ Auth::user()->name }}</span>
        </div>
        <div class="user-detail">
            <strong>Email:</strong>
            <span>{{ Auth::user()->email }}</span>
        </div>
        <div class="user-detail">
            <strong>Email Verified:</strong>
            <span>{{ Auth::user()->email_verified_at ? 'Yes ✅' : 'No ❌' }}</span>
        </div>
        <div class="user-detail">
            <strong>Member Since:</strong>
            <span>{{ Auth::user()->created_at->format('F j, Y') }}</span>
        </div>
        <div class="user-detail">
            <strong>Two-Factor Authentication:</strong>
            <span>{{ Auth::user()->two_factor_enabled ? 'Enabled ✅' : 'Disabled ❌' }}</span>
        </div>
        
        <div class="two-factor-toggle">
            <p><strong>Two-Factor Authentication (2FA)</strong></p>
            <p>Add an extra layer of security to your account by enabling 2FA. You'll receive a code via email each time you log in.</p>
            <form action="{{ route('toggle.2fa') }}" method="POST" style="margin-top: 10px;">
                @csrf
                <button type="submit" class="toggle-btn {{ Auth::user()->two_factor_enabled ? 'disabled' : '' }}">
                    {{ Auth::user()->two_factor_enabled ? 'Disable 2FA' : 'Enable 2FA' }}
                </button>
            </form>
        </div>
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">🔐</div>
            <div class="stat-number">{{ Auth::user()->two_factor_enabled ? 'ON' : 'OFF' }}</div>
            <div class="stat-label">Two-Factor Auth</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">✉️</div>
            <div class="stat-number">{{ Auth::user()->email_verified_at ? 'YES' : 'NO' }}</div>
            <div class="stat-label">Email Verified</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">📅</div>
            <div class="stat-number">{{ Auth::user()->created_at->diffForHumans() }}</div>
            <div class="stat-label">Account Age</div>
        </div>
    </div>

    <div class="features-section">
        <h2>Available Features</h2>
        <div class="features-grid">
            <div class="feature-card">
                <h3>🔒 Secure Authentication</h3>
                <p>Your account is protected with email verification and optional two-factor authentication for maximum security.</p>
            </div>
            <div class="feature-card">
                <h3>📧 Email OTP Verification</h3>
                <p>Receive secure one-time passwords via email for account verification and login authentication.</p>
            </div>
            <div class="feature-card">
                <h3>🛡️ Session Management</h3>
                <p>Advanced session handling with automatic logout and secure token management for your protection.</p>
            </div>
            <div class="feature-card">
                <h3>📱 Responsive Design</h3>
                <p>Access your dashboard from any device with our fully responsive and mobile-friendly interface.</p>
            </div>
        </div>
    </div>
</div>

</body>
</html>
