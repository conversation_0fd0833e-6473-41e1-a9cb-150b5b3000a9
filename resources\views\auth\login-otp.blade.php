<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px;
            margin: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .verify-container {
            max-width: 450px;
            width: 100%;
            background: #fff;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            text-align: center;
        }

        .verify-container h2 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .verify-container p {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .email-display {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 25px;
            font-weight: bold;
            color: #28a745;
        }

        .otp-inputs {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
        }

        .otp-inputs input {
            width: 60px;
            height: 60px;
            font-size: 24px;
            text-align: center;
            border: 2px solid #ddd;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .otp-inputs input:focus {
            outline: none;
            border-color: #28a745;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
        }

        .verify-btn {
            width: 100%;
            padding: 15px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .verify-btn:hover {
            background: #218838;
        }

        .resend-section {
            margin-top: 25px;
            padding-top: 25px;
            border-top: 1px solid #eee;
        }

        .resend-btn {
            background: none;
            color: #007bff;
            border: none;
            text-decoration: underline;
            cursor: pointer;
            font-size: 14px;
        }

        .resend-btn:hover {
            color: #0056b3;
        }

        .alert {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .security-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            color: #856404;
            font-size: 13px;
        }

        .back-link {
            margin-top: 20px;
        }

        .back-link a {
            color: #6c757d;
            text-decoration: none;
            font-size: 14px;
        }

        .back-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>

<div class="verify-container">
    <h2>🔐 Login Verification</h2>
    <p>Enter the 4-digit code sent to your email</p>
    
    <div class="email-display">
        {{ $email }}
    </div>

    @if(session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-error">
            {{ session('error') }}
        </div>
    @endif

    <form id="verify-form" action="{{ route('login.otp.verify') }}" method="POST">
        @csrf

        <input type="hidden" name="token" id="passcode">

        <div class="otp-inputs">
            <input type="text" maxlength="1" required id="digit1" autocomplete="off">
            <input type="text" maxlength="1" required id="digit2" autocomplete="off">
            <input type="text" maxlength="1" required id="digit3" autocomplete="off">
            <input type="text" maxlength="1" required id="digit4" autocomplete="off">
        </div>

        <button type="submit" class="verify-btn">Verify & Login</button>
    </form>

    <div class="resend-section">
        <p>Didn't receive the code?</p>
        <form action="{{ route('resend.otp') }}" method="POST" style="display: inline;">
            @csrf
            <input type="hidden" name="email" value="{{ $email }}">
            <input type="hidden" name="type" value="login">
            <button type="submit" class="resend-btn">Resend Code</button>
        </form>
    </div>

    <div class="security-note">
        <strong>Security Notice:</strong> This code will expire in 5 minutes. Never share this code with anyone.
    </div>

    <div class="back-link">
        <a href="{{ route('signin') }}">← Back to Login</a>
    </div>
</div>

<script>
    document.getElementById('verify-form').addEventListener('submit', function (e) {
        const passcode = [
            document.getElementById('digit1').value,
            document.getElementById('digit2').value,
            document.getElementById('digit3').value,
            document.getElementById('digit4').value
        ].join('');

        if (passcode.length !== 4) {
            e.preventDefault();
            alert('Please enter all 4 digits');
            return;
        }

        document.getElementById('passcode').value = passcode;
    });

    // Auto move to next input and handle backspace
    document.querySelectorAll('.otp-inputs input').forEach((input, index, inputs) => {
        input.addEventListener('input', (e) => {
            if (e.target.value && index < inputs.length - 1) {
                inputs[index + 1].focus();
            }
        });

        input.addEventListener('keydown', (e) => {
            if (e.key === 'Backspace' && !e.target.value && index > 0) {
                inputs[index - 1].focus();
            }
        });

        // Only allow numbers
        input.addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });
    });

    // Focus first input on page load
    document.getElementById('digit1').focus();
</script>

</body>
</html>
