<?php
use App\Http\Controllers\PostController;
use App\Http\Controllers\AuthController;
use Illuminate\Support\Facades\Route;

// Authentication Routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'signin'])->name('signin');
    Route::get('/register', [AuthController::class, 'signup'])->name('signup');
    Route::post('/register', [AuthController::class, 'register'])->name('register');
    Route::post('/login', [AuthController::class, 'login'])->name('login');
    Route::get('/verify', [AuthController::class, 'verify'])->name('verify');
    Route::post('/verify', [AuthController::class, 'verifyEmail'])->name('verify.email');
    Route::post('/resend-otp', [AuthController::class, 'resendOtp'])->name('resend.otp');

    // 2FA Login Routes
    Route::get('/login/otp', [AuthController::class, 'showOtpForm'])->name('login.otp');
    Route::post('/login/otp', [AuthController::class, 'verifyLoginOtp'])->name('login.otp.verify');
});

// Protected Routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', [AuthController::class, 'dashboard'])->name('dashboard');
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
    Route::post('/toggle-2fa', [AuthController::class, 'toggle2FA'])->name('toggle.2fa');

    // Posts routes (protected)
    Route::resource('posts', PostController::class);
});

// Public Routes
Route::get('/', [PostController::class, 'index'])->name('posts.index');