@extends('layouts.app')
@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Posts Management</h4>
                        <a href="{{ route('posts.create') }}" class="btn btn-primary">Create New Post</a>
                    </div>
                    <div class="card-body">
                        @if (session('success'))
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                {{ session('success') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        @endif
                        @if($posts->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Title</th>
                                            <th>Image</th>
                                            <th>Created At</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($posts as $post)
                                            <tr>
                                                <td>{{ $post->id }}</td>
                                                <td>{{ $post->title }}</td>
                                                <td>
                                                    @if($post->image_path)
                                                        <!-- <img src="{{ asset('storage/' . $post->image_path) }}" alt="Post Image"
                                                                                                            width="60" height="60" class="rounded"> -->
                                                        <img src="{{ asset('storage/website/post_images/' . $post->image_path) }}"
                                                            alt="Post Image" width="60" height="60" class="rounded">
                                                    @else
                                                        <span class="text-muted">No Image</span>
                                                    @endif
                                                </td>
                                                <td>{{ $post->created_at->diffForHumans() }}</td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="{{ route('posts.show', $post) }}"
                                                            class="btn btn-info btn-sm">View</a>
                                                        <a href="{{ route('posts.edit', $post) }}"
                                                            class="btn btn-warning btn-sm">Edit</a>
                                                        <form action="{{ route('posts.destroy', $post) }}" method="POST"
                                                            style="display:inline;">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-danger btn-sm"
                                                                onclick="return confirm('Are you sure you want to delete this post?')">
                                                                Delete
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            <div class="d-flex justify-content-center">
                                {{ $posts->links() }}
                            </div>
                        @else
                            <div class="alert alert-info">
                                No posts found. <a href="{{ route('posts.create') }}">Create your first post</a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection