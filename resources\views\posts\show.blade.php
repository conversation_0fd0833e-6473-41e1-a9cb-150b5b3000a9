@extends('layouts.app')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Post Details</h4>
                        <div>
                            <a href="{{ route('posts.edit', $post) }}" class="btn btn-warning btn-sm">Edit</a>
                            <a href="{{ route('posts.index') }}" class="btn btn-secondary btn-sm">Back to Posts</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h2 class="mb-3">{{ $post->title }}</h2>
                            
                            @if($post->image_path)
                                <div class="mb-4 text-center">
                                    <img src="{{ asset('storage/website/post_images/' . $post->image_path) }}" 
                                         alt="Post Image" 
                                         class="img-fluid rounded shadow"
                                         style="max-height: 400px;">
                                </div>
                            @endif
                            
                            <div class="post-body">
                                <p class="lead">{{ $post->body }}</p>
                            </div>
                            
                            <hr>
                            
                            <div class="post-meta text-muted">
                                <small>
                                    <strong>Created:</strong> {{ $post->created_at->format('F j, Y \a\t g:i A') }}
                                    @if($post->updated_at != $post->created_at)
                                        <br><strong>Last Updated:</strong> {{ $post->updated_at->format('F j, Y \a\t g:i A') }}
                                    @endif
                                </small>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('posts.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Posts
                            </a>
                            <div>
                                <a href="{{ route('posts.edit', $post) }}" class="btn btn-warning">
                                    <i class="fas fa-edit"></i> Edit Post
                                </a>
                                <form action="{{ route('posts.destroy', $post) }}" method="POST" style="display:inline;" 
                                      onsubmit="return confirm('Are you sure you want to delete this post?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-trash"></i> Delete Post
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
