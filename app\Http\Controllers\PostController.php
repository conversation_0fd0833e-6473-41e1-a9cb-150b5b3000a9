<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Models\Post;


class PostController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (request()->ajax()){
            return response()->json([
                'posts'=>Post::latest()->get()
            ]);
        }

        $posts = Post::latest()->paginate();
        return view('posts.index', compact('posts'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('posts.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated=$request->validate([
            'title'=>'required|min:5|max:255',
            'body'=>'required|min:10',
            'image'=>'nullable|image|mimes:jpg,jpeg,png,gif|max:2048'
        ]);
       if ($request->hasFile('image')) {
            $validated['image_path'] = $request->file('image')->store('','website');
        }

            Post::create($validated);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Post created successfully.'
            ]);
        }
          return redirect()->route('posts.index')->with('success', 'Post created successfully.');
    }


    /**
     * Display the specified resource.
     */
    public function show(Post $post)
    {
        return view('posts.show', compact('post'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Post $post)
    {
        return view('posts.edit', compact('post'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Post $post)
    {
        $validated = $request->validate([
            'title' => 'required|min:5|max:255',
            'body' => 'required|min:10',
            'image' => 'nullable|image|mimes:jpg,jpeg,png,gif|max:2048'
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($post->image_path) {
                Storage::disk('website')->delete($post->image_path);
            }
            $validated['image_path'] = $request->file('image')->store('', 'website');
        }

        $post->update($validated);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Post updated successfully.'
            ]);
        }

        return redirect()->route('posts.index')->with('success', 'Post updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Post $post)
    {
        // Delete image if exists
        if ($post->image_path) {
            Storage::disk('website')->delete($post->image_path);
        }

        $post->delete();

        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Post deleted successfully.'
            ]);
        }

        return redirect()->route('posts.index')->with('success', 'Post deleted successfully.');
    }
}
