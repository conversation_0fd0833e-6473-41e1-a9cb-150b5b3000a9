<?php

namespace App\Http\Controllers;

use App\Models\EmailVerification;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Mail;
use App\Mail\EmailVerificationMail;
use Exception;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{
    public function signin()
    {
        return view('auth.login');
    }

    public function signup()
    {
        return view('auth.register');
    }

    public function register(Request $request){

        $request->validate([
            'name'=>'required|string|max:255',
            'email'=>'required|email|unique:users|max:255',
            'password'=>'required|string|min:8|confirmed'
        ]);

        try{
            // Clean up any existing verification tokens for this email
            EmailVerification::where('email', $request->email)
                           ->where('type', 'registration')
                           ->delete();

            // Generate a 4 digit passcode
            $token = random_int(1000, 9999);

            EmailVerification::create([
                'email' => $request->email,
                'token' => $token,
                'type' => 'registration',
                'expires_at' => Carbon::now()->addMinutes(2),
                'attempts' => 0
            ]);

            Session::put('user_data', [
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password)
            ]);

            Mail::to($request->email)->send(new EmailVerificationMail($token, 'registration'));

            return redirect()->route('verify')->with('success', 'Verification code sent to your email');
        }catch(Exception $e){
            Log::error('Registration error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }

    public function verify(){
        $email = Session::get('user_data.email');
        if(!$email){
            return redirect()->route('login')->with('error', 'Email not Found');
        }

        return view('auth.verify', compact('email'));
    }

    public function verifyEmail(Request $request){
        try{
            $request->validate([
                'token' => 'required|string|size:4'
            ]);

            $email = Session::get('user_data.email');

            if(!$email){
                return redirect()->route('signup')->with('error', 'Session expired. Please register again.');
            }

            $emailVerification = EmailVerification::where('token', $request->token)
                                                 ->where('email', $email)
                                                 ->where('type', 'registration')
                                                 ->first();

            if(!$emailVerification){
                return redirect()->back()->with('error', 'Invalid verification code');
            }

            if($emailVerification->isExpired()){
                $emailVerification->delete();
                return redirect()->back()->with('error', 'Verification code expired. Please request a new one.');
            }

            if(!$emailVerification->isValid()){
                return redirect()->back()->with('error', 'Too many failed attempts. Please request a new code.');
            }

            // Increment attempts
            $emailVerification->incrementAttempts();

            $user = User::create([
                'name' => Session::get('user_data.name'),
                'email' => Session::get('user_data.email'),
                'password' => Session::get('user_data.password'),
                'email_verified_at' => now()
            ]);

            Session::forget('user_data');
            $emailVerification->delete();

            Auth::login($user);

            return redirect()->route('dashboard')->with('success', 'Email verified successfully! Welcome to your dashboard.');
        }catch(Exception $e){
            Log::error('Email verification error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }

    public function login(Request $request){
        $request->validate([
            'email' => 'required|email|exists:users,email',
            'password' => 'required|string'
        ]);

        $loginCredentials = $request->only('email', 'password');
        $user = User::where('email', $request->email)->first();

        // Check if user has 2FA enabled
        if ($user && $user->two_factor_enabled) {
            if (Auth::once($loginCredentials)) {
                // Generate OTP for 2FA
                $token = random_int(1000, 9999);

                // Clean up any existing login OTPs
                EmailVerification::where('email', $user->email)
                               ->where('type', 'login')
                               ->delete();

                // Create new OTP
                EmailVerification::create([
                    'email' => $user->email,
                    'token' => $token,
                    'type' => 'login',
                    'expires_at' => Carbon::now()->addMinutes(5),
                    'attempts' => 0
                ]);

                // Store user ID in session for the second step
                Session::put('auth.2fa_user_id', $user->id);

                // Send OTP email
                Mail::to($user->email)->send(new EmailVerificationMail($token, 'login'));

                return redirect()->route('login.otp')->with('success', 'Please enter the verification code sent to your email');
            }

            return redirect()->back()->with('error', 'Invalid credentials');
        }

        // Regular login without 2FA
        if (Auth::attempt($loginCredentials)) {
            $request->session()->regenerate();
            return redirect()->intended(route('dashboard'))->with('success', 'Login successful');
        }

        return redirect()->back()->with('error', 'Invalid credentials');
    }

    public function showOtpForm() {
        if (!Session::has('auth.2fa_user_id')) {
            return redirect()->route('signin');
        }

        $user = User::find(Session::get('auth.2fa_user_id'));
        return view('auth.login-otp', ['email' => $user->email]);
    }

    public function verifyLoginOtp(Request $request) {
        $request->validate([
            'token' => 'required|string|size:4'
        ]);

        if (!Session::has('auth.2fa_user_id')) {
            return redirect()->route('signin')->with('error', 'Session expired. Please login again.');
        }

        $userId = Session::get('auth.2fa_user_id');
        $user = User::find($userId);

        if (!$user) {
            Session::forget('auth.2fa_user_id');
            return redirect()->route('signin')->with('error', 'User not found');
        }

        $verification = EmailVerification::where('email', $user->email)
                                       ->where('token', $request->token)
                                       ->where('type', 'login')
                                       ->first();

        if (!$verification) {
            return redirect()->back()->with('error', 'Invalid verification code');
        }

        if (!$verification->isValid()) {
            if ($verification->isExpired()) {
                $verification->delete();
                return redirect()->back()->with('error', 'Verification code expired. Please login again.');
            }

            $verification->incrementAttempts();
            return redirect()->back()->with('error', 'Invalid verification code');
        }

        // Login the user
        Auth::loginUsingId($userId);
        Session::forget('auth.2fa_user_id');
        $verification->delete();

        return redirect()->intended(route('dashboard'))->with('success', 'Login successful');
    }

    public function logout(Request $request){
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect()->route('signin')->with('success', 'You have been logged out');
    }

    public function dashboard(){
        return view('dashboard');
    }

    public function resendOtp(Request $request) {
        $request->validate([
            'email' => 'required|email'
        ]);

        $email = $request->email;
        $type = $request->input('type', 'registration');

        // Check if we have this email in session for registration
        if ($type === 'registration' && (!Session::has('user_data.email') || Session::get('user_data.email') !== $email)) {
            return redirect()->route('signup')->with('error', 'Session expired. Please register again.');
        }

        // For login OTP, check if we have the user in session
        if ($type === 'login' && !Session::has('auth.2fa_user_id')) {
            return redirect()->route('signin')->with('error', 'Session expired. Please login again.');
        }

        // Clean up existing tokens
        EmailVerification::where('email', $email)
                       ->where('type', $type)
                       ->delete();

        // Generate new token
        $token = random_int(1000, 9999);

        // Set expiration time based on type
        $expiresAt = $type === 'login' ? Carbon::now()->addMinutes(5) : Carbon::now()->addMinutes(2);

        // Create new verification record
        EmailVerification::create([
            'email' => $email,
            'token' => $token,
            'type' => $type,
            'expires_at' => $expiresAt,
            'attempts' => 0
        ]);

        // Send email
        Mail::to($email)->send(new EmailVerificationMail($token, $type));

        return redirect()->back()->with('success', 'A new verification code has been sent to your email');
    }

    public function toggle2FA() {
        $user = Auth::user();
        $user->two_factor_enabled = !$user->two_factor_enabled;
        $user->save();

        $status = $user->two_factor_enabled ? 'enabled' : 'disabled';
        return redirect()->back()->with('success', "Two-factor authentication has been {$status}");
    }
}

