<?php

namespace App\Http\Controllers;

use App\Models\EmailVerification;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Mail;
use App\Mail\EmailVerificationMail;
use Exception;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{
    public function signin()
    {
        return view('auth.login');
    }

    public function signup()
    {
        return view('auth.register');
    }

    public function register(Request $request){
        
        $request->validate([
            'name'=>'required|string',
            'email'=>'required|email|unique:users',
            'password'=>'required|string|min:8|confirmed'
        ]);

        try{
            // Geneate a 4 digit passcode
            $token =  random_int(1000, 9999);

            EmailVerification::create([
                'email'=>$request->email,
                'token'=>$token,
                'expires_at'=> Carbon::now()->addMinutes(2)
            ]);

            Session::put('user_data', [
                'name'=> $request->name,
                'email'=> $request->email,
                'password'=> Hash::make($request->password)
            ]);

            Mail::to($request->email)->send(new EmailVerificationMail($token));

            return redirect()->route('verify');
        }catch(Exception $e){
            Log::info($e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong');
        }
    }

    public function verify(){
        $email = Session::get('user_data.email');
        if(!$email){
            return redirect()->route('login')->with('error', 'Email not Found');
        }

        return view('auth.verify', compact('email'));
    }

    public function verifyEmail(Request $request){
        try{
            $request->validate([
                'token'=>'required|exists:email_verifications,token'
            ]);

            $email = Session::get('user_data.email');

            if(!$email){
                return redirect()->back()->with('error', 'Email not Found');
            }

            $emailVerification = EmailVerification::where('token', $request->token)->first();

            if($emailVerification->expires_at < Carbon::now()){
                return redirect()->back()->with('error', 'Token expired');
            }

            $user = User::create([
                'name'=> Session::get('user_data.name'),
                'email'=> Session::get('user_data.email'),
                'password'=> Session::get('user_data.password'),
                'email_verified_at'=> now()
            ]);

            Session::forget('user_data');

            $emailVerification->delete();

            Auth::login($user);

            return redirect()->route('signin')->with('success', 'Email verified successfully');
        }catch(Exception $e){
            Log::info($e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong');
        }
    }

    public function login(Request $request){
        $request->validate([
            'email'=>'required|email',
            'password'=>'required|string'
        ]);

        $loginCredentials = $request->only('email', 'password');

        if(Auth::attempt($loginCredentials)){
            return redirect()->route('dashboard');
        }else{
            return redirect()->back()->with('error', 'Invalid credentials');
        }
    }

    public function logout(){

    }

    public function dashboard(){
        return " Welcome TO Dashboard";
    }
}

