<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class EmailVerification extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'email',
        'token',
        'expires_at',
        'type', // 'registration', 'login', 'password_reset'
        'attempts',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'expires_at' => 'datetime',
        'attempts' => 'integer',
    ];

    /**
     * Check if the token is expired
     *
     * @return bool
     */
    public function isExpired(): bool
    {
        return $this->expires_at < Carbon::now();
    }

    /**
     * Check if the token is still valid (not expired and not exceeded max attempts)
     *
     * @return bool
     */
    public function isValid(): bool
    {
        return !$this->isExpired() && $this->attempts < 3;
    }

    /**
     * Increment the attempts counter
     *
     * @return void
     */
    public function incrementAttempts(): void
    {
        $this->increment('attempts');
    }

    /**
     * Scope to get valid tokens
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeValid($query)
    {
        return $query->where('expires_at', '>', Carbon::now())
                    ->where('attempts', '<', 3);
    }

    /**
     * Scope to get tokens by email and type
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $email
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForEmailAndType($query, $email, $type = 'registration')
    {
        return $query->where('email', $email)->where('type', $type);
    }

    /**
     * Clean up expired tokens
     *
     * @return int
     */
    public static function cleanupExpired(): int
    {
        return static::where('expires_at', '<', Carbon::now())->delete();
    }
}
