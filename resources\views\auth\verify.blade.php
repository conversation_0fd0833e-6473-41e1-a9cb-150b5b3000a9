
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Verify Email</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f1f1f1;
            padding: 30px;
        }

        .verify-container {
            max-width: 400px;
            margin: auto;
            background: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .otp-inputs input {
            width: 50px;
            height: 50px;
            font-size: 24px;
            text-align: center;
            margin: 0 5px;
        }

        button {
            margin-top: 20px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        .resend-link {
            margin-top: 15px;
            display: block;
        }
    </style>
</head>
<body>

<div class="verify-container">
    <h2>Verify Your Email</h2>
    <p>Enter the 4-digit code sent to your email</p>

    <form id="verify-form" action="{{ route('verify.email') }}" method="POST">
        @csrf

        <input type="hidden" name="token" id="passcode">
        <input type="hidden" name="email" value="{{ $email }}">

        <div class="otp-inputs">
            <input type="text" maxlength="1" required id="digit1">
            <input type="text" maxlength="1" required id="digit2">
            <input type="text" maxlength="1" required id="digit3">
            <input type="text" maxlength="1" required id="digit4">
        </div>

        <button type="submit">Verify</button>
    </form>

    <form action="" method="POST" class="resend-link">
        <input type="hidden" name="email" value="">
        <button type="submit" style="background: none; color: blue; border: none; text-decoration: underline;">Resend Code</button>
    </form>
</div>

<script>
    document.getElementById('verify-form').addEventListener('submit', function (e) {
        const passcode = [
            document.getElementById('digit1').value,
            document.getElementById('digit2').value,
            document.getElementById('digit3').value,
            document.getElementById('digit4').value
        ].join('');

        document.getElementById('passcode').value = passcode;
    });

    // Auto move to next input
    document.querySelectorAll('.otp-inputs input').forEach((input, index, inputs) => {
        input.addEventListener('input', () => {
            if (input.value && index < inputs.length - 1) {
                inputs[index + 1].focus();
            }
        });
    });
</script>

</body>
</html>
