<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login OTP Code</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: #28a745;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 0 0 8px 8px;
        }
        .otp-code {
            background: #fff;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
            font-size: 32px;
            font-weight: bold;
            letter-spacing: 8px;
            color: #28a745;
        }
        .warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            color: #721c24;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Login Verification</h1>
    </div>
    
    <div class="content">
        <h2>Secure Login</h2>
        <p>Someone is trying to log into your account. If this was you, please use the code below to complete your login:</p>
        
        <div class="otp-code">
            {{ $token }}
        </div>
        
        <div class="warning">
            <strong>Security Alert:</strong> This code will expire in 5 minutes. If you didn't attempt to log in, please secure your account immediately and contact support.
        </div>
        
        <p>For your security, never share this code with anyone.</p>
        
        <p>Best regards,<br>{{ config('app.name') }} Security Team</p>
    </div>
    
    <div class="footer">
        <p>This is an automated security message, please do not reply to this email.</p>
    </div>
</body>
</html>
